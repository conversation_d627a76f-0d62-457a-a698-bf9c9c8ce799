using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Windows;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Winmug.Core.Models;

namespace Winmug.ViewModels;

/// <summary>
/// Custom logger that captures log messages and displays them in the UI
/// </summary>
public class UILogger : ILogger
{
    private readonly Action<string> _addLogMessage;

    public UILogger(Action<string> addLogMessage)
    {
        _addLogMessage = addLogMessage;
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

    public bool IsEnabled(LogLevel logLevel) => true;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        var message = formatter(state, exception);
        var prefix = logLevel switch
        {
            LogLevel.Error => "❌ ERROR",
            LogLevel.Warning => "⚠ WARNING",
            LogLevel.Information => "ℹ INFO",
            LogLevel.Debug => "🔍 DEBUG",
            _ => logLevel.ToString().ToUpper()
        };

        _addLogMessage($"{prefix}: {message}");

        if (exception != null)
        {
            _addLogMessage($"   Exception: {exception.Message}");
        }
    }
}

/// <summary>
/// ViewModel for the main application window
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ISmugMugApiClient _apiClient;
    private readonly IDownloadManager _downloadManager;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private bool _isAuthenticated;

    [ObservableProperty]
    private string? _userNickname;

    [ObservableProperty]
    private string _authenticationStatus = "Not authenticated";

    [ObservableProperty]
    private string? _targetDirectory;

    [ObservableProperty]
    private bool _isOperationInProgress;

    [ObservableProperty]
    private bool _isWaitingForVerificationCode;

    [ObservableProperty]
    private FolderNode? _folderStructure;

    [ObservableProperty]
    private bool _isFolderStructureLoaded;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private ObservableCollection<string> _logMessages = new();

    [ObservableProperty]
    private bool _canStartDownload;

    [ObservableProperty]
    private bool _canPauseDownload;

    [ObservableProperty]
    private bool _canResumeDownload;

    [ObservableProperty]
    private bool _canCancelDownload;

    [ObservableProperty]
    private double _overallProgress;

    [ObservableProperty]
    private double _currentFileProgress;

    [ObservableProperty]
    private string _progressText = string.Empty;

    [ObservableProperty]
    private string _downloadSpeed = string.Empty;

    [ObservableProperty]
    private string _estimatedTimeRemaining = string.Empty;

    // Authentication flow state
    private string? _requestToken;
    private string? _requestTokenSecret;
    private string? _authorizationUrl;

    public MainWindowViewModel(
        ISmugMugAuthenticationService authService,
        ISmugMugApiClient apiClient,
        IDownloadManager downloadManager,
        ILogger<MainWindowViewModel> logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        _downloadManager = downloadManager;
        _logger = logger;

        // Subscribe to authentication events
        _authService.AuthenticationStatusChanged += OnAuthenticationStatusChanged;

        // Subscribe to download events
        _downloadManager.ProgressUpdated += OnDownloadProgressUpdated;
        _downloadManager.StatusChanged += OnDownloadStatusChanged;
        _downloadManager.ErrorOccurred += OnDownloadErrorOccurred;

        // Initialize authentication state
        IsAuthenticated = _authService.IsAuthenticated;
        UpdateAuthenticationStatus();

        // Load stored credentials if available
        _ = Task.Run(async () =>
        {
            try
            {
                var hasStoredCredentials = await _authService.LoadStoredCredentialsAsync();
                if (hasStoredCredentials)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLogMessage("✓ Stored credentials loaded successfully");
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load stored credentials on startup");
            }
        });
    }

    [RelayCommand]
    private async Task TestOAuthAsync()
    {
        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing OAuth signature generation...";
            AddLogMessage("Starting OAuth diagnostic test...");

            // Create OAuth tester with a custom logger that captures output to UI
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("User-Agent", "Winmug/1.0");

            var tester = new Winmug.Core.Authentication.OAuthTester(httpClient, _logger);

            // Get credentials from auth service
            var credentials = _authService.Credentials;

            AddLogMessage($"Testing with Consumer Key: {credentials.ConsumerKey.Substring(0, 8)}...");

            var (success, errorDetails) = await tester.TestRealOAuthRequestAsync(credentials.ConsumerKey, credentials.ConsumerSecret);

            if (success)
            {
                AddLogMessage("✓ OAuth test successful! Credentials and signature generation are working.");
                StatusMessage = "OAuth test successful";
            }
            else
            {
                AddLogMessage("❌ OAuth test failed. Detailed error information:");
                AddLogMessage("");

                // Split the error details into multiple lines for better readability
                var lines = errorDetails.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    AddLogMessage($"   {line}");
                }

                AddLogMessage("");
                AddLogMessage("Next steps:");
                AddLogMessage("  1. Verify your SmugMug API credentials are correct");
                AddLogMessage("  2. Check that your system clock is synchronized");
                AddLogMessage("  3. Ensure your API key has OAuth permissions");
                StatusMessage = "OAuth test failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OAuth test failed");
            AddLogMessage($"❌ OAuth test error: {ex.Message}");
            StatusMessage = "OAuth test error";
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task InitiateAuthenticationAsync()
    {
        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Initiating authentication...";
            AddLogMessage("Starting SmugMug authentication process...");

            var requestTokenResponse = await _authService.InitiateAuthenticationAsync();

            _requestToken = requestTokenResponse.Token;
            _requestTokenSecret = requestTokenResponse.TokenSecret;
            _authorizationUrl = requestTokenResponse.AuthorizationUrl;

            AddLogMessage($"✓ Request token obtained successfully");
            AddLogMessage($"🔍 DEBUG: Full authorization URL: {_authorizationUrl}");

            // Check if the URL contains the required parameters
            if (_authorizationUrl.Contains("Access=Full"))
            {
                AddLogMessage("✓ URL contains Access=Full parameter");
            }
            else
            {
                AddLogMessage("❌ WARNING: URL missing Access=Full parameter!");
            }

            if (_authorizationUrl.Contains("Permissions=Read"))
            {
                AddLogMessage("✓ URL contains Permissions=Read parameter");
            }
            else
            {
                AddLogMessage("❌ WARNING: URL missing Permissions=Read parameter!");
            }

            // Try to open the authorization URL in the default browser with better error handling
            bool browserOpened = false;
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = _authorizationUrl,
                    UseShellExecute = true
                };

                var process = Process.Start(processInfo);
                browserOpened = process != null;

                if (browserOpened)
                {
                    AddLogMessage("✓ Browser opened successfully");
                }
            }
            catch (Exception browserEx)
            {
                _logger.LogWarning(browserEx, "Failed to open browser automatically");
                AddLogMessage($"⚠ Could not open browser automatically: {browserEx.Message}");
            }

            if (!browserOpened)
            {
                // Fallback: Show the URL to the user
                AddLogMessage("Please manually copy and paste this URL into your browser:");
                AddLogMessage(_authorizationUrl);

                // Try to copy to clipboard
                try
                {
                    Clipboard.SetText(_authorizationUrl);
                    AddLogMessage("✓ URL copied to clipboard");
                }
                catch
                {
                    // Ignore clipboard errors
                }
            }

            StatusMessage = "Please log in to SmugMug in your browser. Authentication will complete automatically.";
            AddLogMessage("");
            AddLogMessage("🌐 WEB-BASED AUTHENTICATION:");
            AddLogMessage("1. Log in to SmugMug with your username and password");
            AddLogMessage("2. Click 'Authorize' to grant FULL ACCESS to Winmug");
            AddLogMessage("3. Authentication will complete automatically - no verification code needed!");
            AddLogMessage("⏳ Waiting for browser authentication to complete...");

            // Start web-based authentication automatically
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(5000); // Give user time to complete browser auth

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLogMessage("🔄 Attempting to complete web-based authentication...");
                    });

                    var accessTokenResponse = await _authService.CompleteWebBasedAuthenticationAsync(
                        _requestToken!, _requestTokenSecret!);

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        UserNickname = accessTokenResponse.UserNickname;
                        StatusMessage = $"Successfully authenticated as {UserNickname}";
                        AddLogMessage($"✅ Web-based authentication completed successfully!");

                        // Clear temporary authentication state
                        _requestToken = null;
                        _requestTokenSecret = null;
                        _authorizationUrl = null;
                        IsWaitingForVerificationCode = false;
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLogMessage($"❌ Web-based authentication failed: {ex.Message}");
                        AddLogMessage("💡 You can still try the manual verification code method if needed");
                        IsWaitingForVerificationCode = true; // Fall back to manual method
                    });
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease check your internet connection and try again.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task CompleteAuthenticationAsync(string verificationCode)
    {
        // Validate inputs
        if (string.IsNullOrWhiteSpace(verificationCode))
        {
            MessageBox.Show("Please enter the 6-digit verification code from SmugMug.",
                "Missing Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (_requestToken == null || _requestTokenSecret == null)
        {
            MessageBox.Show("Please click 'Authenticate with SmugMug' first to start the authentication process.",
                "Authentication Not Started", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Clean and validate verification code format
        var cleanCode = verificationCode.Trim().Replace("-", "").Replace(" ", "");
        if (cleanCode.Length != 6 || !cleanCode.All(char.IsDigit))
        {
            MessageBox.Show("The verification code should be exactly 6 digits. Please check and try again.",
                "Invalid Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Completing authentication...";
            AddLogMessage($"Completing authentication with verification code: {cleanCode}");

            var accessTokenResponse = await _authService.CompleteAuthenticationAsync(
                cleanCode, _requestToken, _requestTokenSecret);

            UserNickname = accessTokenResponse.UserNickname;
            StatusMessage = $"Successfully authenticated as {UserNickname}";
            AddLogMessage($"✓ Authentication completed successfully for user: {UserNickname}");

            // Verify we have private access
            AddLogMessage("Verifying access permissions...");
            var hasPrivateAccess = await _apiClient.VerifyPrivateAccessAsync();
            if (hasPrivateAccess)
            {
                AddLogMessage("✓ Private access verified - can access your private photos");
            }
            else
            {
                AddLogMessage("⚠ Warning: Only public access detected - may not be able to access private photos");
                StatusMessage += " (Limited access - check permissions)";
            }

            // Clear temporary authentication state
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;
            IsWaitingForVerificationCode = false;

            AddLogMessage("✓ Authentication process completed successfully!");
            AddLogMessage($"Access token: {accessTokenResponse.Token.Substring(0, 8)}...");
            AddLogMessage($"User authenticated: {UserNickname}");

            // Test OAuth signature generation first
            AddLogMessage("🔍 Testing OAuth signature generation...");
            try
            {
                OAuthTestHelper.TestOAuthSignatures(_logger);
                OAuthTestHelper.TestUrlEncoding(_logger);
                AddLogMessage("✓ OAuth signature test completed - check debug output for details");
            }
            catch (Exception oauthTestEx)
            {
                AddLogMessage($"❌ OAuth signature test failed: {oauthTestEx.Message}");
            }

            // Test the credentials immediately using the authentication service
            AddLogMessage("🔍 Testing credentials immediately after OAuth completion...");
            try
            {
                var credentialsValid = await _authService.TestCredentialsAsync();
                if (credentialsValid)
                {
                    AddLogMessage("✓ Credential test successful using authentication service!");

                    // Now test with the API client
                    AddLogMessage("🔍 Testing with API client...");
                    var testUser = await _apiClient.GetAuthenticatedUserAsync();
                    AddLogMessage($"✓ API client test successful! User: {testUser.Name}");
                }
                else
                {
                    AddLogMessage("❌ Credential test failed using authentication service");
                    AddLogMessage("   This indicates an issue with OAuth signature generation or credential storage");
                }
            }
            catch (Exception testEx)
            {
                AddLogMessage($"❌ Credential test failed: {testEx.Message}");
                AddLogMessage($"   This indicates an issue with the OAuth flow or credential handling");
                AddLogMessage($"   Exception type: {testEx.GetType().Name}");
                if (testEx.InnerException != null)
                {
                    AddLogMessage($"   Inner exception: {testEx.InnerException.Message}");
                }
            }
        }
        catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401"))
        {
            _logger.LogError(httpEx, "Authentication failed with 401 Unauthorized");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication failed: Invalid verification code or expired session");
            AddLogMessage("Please try the authentication process again from the beginning.");

            // Clear temporary state on 401 error
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;
            IsWaitingForVerificationCode = false;

            MessageBox.Show("Authentication failed. The verification code may be incorrect or expired.\n\nPlease click 'Authenticate with SmugMug' to start over.",
                "Authentication Failed", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication completion error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease try again or contact support if the problem persists.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            await _authService.ClearStoredCredentialsAsync();
            StatusMessage = "Logged out successfully";
            AddLogMessage("User logged out and credentials cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to logout");
            AddLogMessage($"Logout error: {ex.Message}");
        }
    }

    [RelayCommand]
    private void SelectTargetDirectory()
    {
        var dialog = new Microsoft.Win32.OpenFolderDialog
        {
            Title = "Select Download Directory",
            Multiselect = false
        };

        if (dialog.ShowDialog() == true)
        {
            TargetDirectory = dialog.FolderName;
            AddLogMessage($"Target directory selected: {TargetDirectory}");
        }
    }

    [RelayCommand]
    private async Task LoadFolderStructureAsync()
    {
        if (!IsAuthenticated)
        {
            AddLogMessage("❌ Please authenticate first before loading folder structure");
            MessageBox.Show("Please authenticate first before loading folder structure", "Not Authenticated", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Add a simple test message to verify logging is working
        AddLogMessage("🔍 STARTING FOLDER STRUCTURE LOAD - Testing logging...");
        MessageBox.Show("Starting folder structure load. Check logs for detailed progress.", "Debug", MessageBoxButton.OK, MessageBoxImage.Information);

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Loading folder structure...";
            AddLogMessage("=== STARTING FOLDER STRUCTURE LOAD ===");
            AddLogMessage($"Authentication status: {IsAuthenticated}");
            AddLogMessage($"User nickname: {UserNickname ?? "Unknown"}");

            // Step 1: Test basic API access
            AddLogMessage("Step 1: Testing basic API access...");
            try
            {
                var user = await _apiClient.GetAuthenticatedUserAsync();
                AddLogMessage($"✓ API access confirmed for user: {user.Name}");
                AddLogMessage($"  User ID: {user.NickName}");
                AddLogMessage($"  User URI: {user.Uri}");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Step 1 failed: {ex.Message}");
                throw;
            }

            // Step 2: Verify access permissions and provide detailed guidance
            AddLogMessage("Step 2: Verifying access permissions...");
            AccessLevelInfo accessInfo;
            try
            {
                accessInfo = await _apiClient.GetAccessLevelInfoAsync();
                AddLogMessage($"✓ Access Level: {accessInfo.AccessLevel}");
                AddLogMessage($"  Summary: {accessInfo.Summary}");

                if (accessInfo.CanAccessPrivateContent)
                {
                    AddLogMessage("✓ Full access confirmed - can access private photos and folders");
                }
                else
                {
                    AddLogMessage("⚠ LIMITED ACCESS DETECTED");
                    foreach (var recommendation in accessInfo.Recommendations)
                    {
                        AddLogMessage($"  {recommendation}");
                    }
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Step 2 failed: {ex.Message}");
                throw;
            }

            // Step 3: Get root node
            AddLogMessage("Step 3: Getting user root node...");
            SmugMugNode rootNode;
            try
            {
                rootNode = await _apiClient.GetUserRootNodeAsync();
                AddLogMessage($"✓ Root node found: {rootNode.Name} (ID: {rootNode.NodeId})");
                AddLogMessage($"  Root node type: {rootNode.Type}");
                AddLogMessage($"  Root node privacy: {rootNode.Privacy}");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Step 3 failed: {ex.Message}");
                throw;
            }

            // Step 4: Test getting child nodes
            AddLogMessage("Step 4: Testing child node access...");
            try
            {
                var childNodes = await _apiClient.GetChildNodesAsync(rootNode.NodeId);
                AddLogMessage($"✓ Found {childNodes.Count} child nodes in root");

                foreach (var child in childNodes.Take(5)) // Show first 5
                {
                    AddLogMessage($"  - {child.Name} ({child.Type}) - Privacy: {child.Privacy}");
                }

                if (childNodes.Count > 5)
                {
                    AddLogMessage($"  ... and {childNodes.Count - 5} more nodes");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Step 4 failed: {ex.Message}");
                throw;
            }

            // Step 5: Load complete folder structure
            AddLogMessage("Step 5: Loading complete folder structure...");
            try
            {
                FolderStructure = await _apiClient.GetFolderStructureAsync();
                IsFolderStructureLoaded = true;

                StatusMessage = "Folder structure loaded successfully";
                AddLogMessage($"✓ Folder structure loaded successfully!");
                AddLogMessage($"  Total images: {FolderStructure.TotalImageCount}");
                AddLogMessage($"  Total folders: {CountTotalFolders(FolderStructure)}");
                AddLogMessage($"  Total estimated size: {FolderStructure.TotalEstimatedSize}");
                AddLogMessage("=== FOLDER STRUCTURE LOAD COMPLETE ===");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Step 5 failed: {ex.Message}");
                throw;
            }
        }
        catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401") || httpEx.Message.Contains("Unauthorized"))
        {
            _logger.LogError(httpEx, "Authentication failed when loading folder structure");
            StatusMessage = "Authentication failed";
            AddLogMessage("❌ AUTHENTICATION FAILURE DETECTED");
            AddLogMessage($"  Error: {httpEx.Message}");
            AddLogMessage("  This usually means:");
            AddLogMessage("  1. Your session has expired");
            AddLogMessage("  2. You have limited access permissions");
            AddLogMessage("  3. The access token is invalid");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Click 'Logout' then 'Authenticate with SmugMug' to get fresh credentials");

            MessageBox.Show("Authentication failed. Your session may have expired or you have limited access permissions.\n\nPlease logout and authenticate again to get full access.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (InvalidOperationException invOpEx) when (invOpEx.Message.Contains("OAuth permissions") || invOpEx.Message.Contains("limited access"))
        {
            _logger.LogError(invOpEx, "OAuth permission issue when loading folder structure");
            StatusMessage = "Limited access permissions";
            AddLogMessage("❌ LIMITED ACCESS PERMISSIONS DETECTED");
            AddLogMessage($"  Error: {invOpEx.Message}");
            AddLogMessage("  This means you have limited access to SmugMug.");
            AddLogMessage("  To access your folder structure:");
            AddLogMessage("  1. Click 'Logout' to clear current credentials");
            AddLogMessage("  2. Click 'Authenticate with SmugMug' to start fresh");
            AddLogMessage("  3. When prompted, make sure to click 'Authorize' or 'Allow'");
            AddLogMessage("  4. Grant full access permissions when asked");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Re-authenticate with full access permissions");

            MessageBox.Show("You have limited access to SmugMug and cannot access your folder structure.\n\nTo fix this:\n1. Click 'Logout' to clear current credentials\n2. Click 'Authenticate with SmugMug' to start fresh\n3. When prompted, make sure to grant full access permissions",
                "Limited Access Permissions", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (UriFormatException uriEx)
        {
            _logger.LogError(uriEx, "URI format exception when loading folder structure");
            StatusMessage = "Invalid user data from SmugMug";
            AddLogMessage("❌ INVALID USER DATA DETECTED");
            AddLogMessage($"  Error: {uriEx.Message}");
            AddLogMessage("  This indicates a problem with the user data from SmugMug.");
            AddLogMessage("  This usually happens with limited access permissions.");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Re-authenticate with full access permissions");

            MessageBox.Show("Invalid user data received from SmugMug. This usually indicates limited access permissions.\n\nPlease logout and authenticate again with full access permissions.",
                "Invalid User Data", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load folder structure");
            StatusMessage = "Failed to load folder structure";
            AddLogMessage($"❌ UNEXPECTED ERROR: {ex.Message}");
            AddLogMessage($"  Exception type: {ex.GetType().Name}");

            if (ex.InnerException != null)
            {
                AddLogMessage($"  Inner error: {ex.InnerException.Message}");
                AddLogMessage($"  Inner exception type: {ex.InnerException.GetType().Name}");
            }

            AddLogMessage($"  Stack trace: {ex.StackTrace}");

            MessageBox.Show($"Failed to load folder structure: {ex.Message}\n\nCheck the logs for detailed error information.",
                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    private int CountTotalFolders(FolderNode folder)
    {
        int count = 1; // Count this folder
        foreach (var child in folder.Children)
        {
            count += CountTotalFolders(child);
        }
        return count;
    }

    [RelayCommand]
    private async Task TestApiConnectionAsync()
    {
        if (!IsAuthenticated)
        {
            MessageBox.Show("Please authenticate first.", "Not Authenticated", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing API connection...";
            AddLogMessage("Testing SmugMug API connection...");

            var user = await _apiClient.GetAuthenticatedUserAsync();
            var accessInfo = await _apiClient.GetAccessLevelInfoAsync();

            StatusMessage = "API connection successful";
            AddLogMessage($"API test successful. User: {user.Name}");
            AddLogMessage($"Access Level: {accessInfo.AccessLevel}");
            AddLogMessage($"Summary: {accessInfo.Summary}");

            var message = $"API connection successful!\nUser: {user.Name}\nAccess Level: {accessInfo.AccessLevel}\n\n{accessInfo.Summary}";

            if (!accessInfo.CanAccessPrivateContent)
            {
                message += "\n\nRecommendations:";
                foreach (var recommendation in accessInfo.Recommendations)
                {
                    message += $"\n• {recommendation}";
                }
            }

            // Try to get root node if possible
            try
            {
                var rootNode = await _apiClient.GetUserRootNodeAsync();
                AddLogMessage($"Root node: {rootNode.Name}");
                message += $"\nRoot node: {rootNode.Name}";
            }
            catch (Exception rootEx)
            {
                AddLogMessage($"Root node access failed: {rootEx.Message}");
                message += $"\nRoot node: Not accessible ({rootEx.Message})";
            }

            MessageBox.Show(message, "API Test Results", MessageBoxButton.OK,
                accessInfo.CanAccessPrivateContent ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API connection test failed");
            StatusMessage = "API connection failed";
            AddLogMessage($"API test failed: {ex.Message}");
            MessageBox.Show($"API connection failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task StartDownloadAsync()
    {
        if (string.IsNullOrEmpty(TargetDirectory))
        {
            MessageBox.Show("Please select a target directory first.", "No Target Directory",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (!IsAuthenticated)
        {
            MessageBox.Show("Please authenticate first.", "Not Authenticated",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            UpdateDownloadButtonStates();
            AddLogMessage("Starting download process...");

            await _downloadManager.StartDownloadAsync(TargetDirectory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Download failed");
            AddLogMessage($"Download failed: {ex.Message}");
            MessageBox.Show($"Download failed: {ex.Message}", "Download Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
            UpdateDownloadButtonStates();
        }
    }

    [RelayCommand]
    private async Task PauseDownloadAsync()
    {
        try
        {
            await _downloadManager.PauseAsync();
            AddLogMessage("Download paused");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause download");
            AddLogMessage($"Failed to pause download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ResumeDownloadAsync()
    {
        try
        {
            await _downloadManager.ResumeAsync();
            AddLogMessage("Download resumed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume download");
            AddLogMessage($"Failed to resume download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task CancelDownloadAsync()
    {
        try
        {
            await _downloadManager.CancelAsync();
            AddLogMessage("Download cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel download");
            AddLogMessage($"Failed to cancel download: {ex.Message}");
        }
    }

    private void OnDownloadProgressUpdated(object? sender, DownloadProgressEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var stats = e.Statistics;
            OverallProgress = stats.OverallProgressPercentage;
            ProgressText = $"{stats.DownloadedPhotos}/{stats.TotalPhotos} photos ({stats.ProcessedAlbums}/{stats.TotalAlbums} albums)";

            if (stats.AverageDownloadSpeed.HasValue)
            {
                DownloadSpeed = FormatBytes(stats.AverageDownloadSpeed.Value) + "/s";
            }

            if (stats.EstimatedTimeRemaining.HasValue)
            {
                EstimatedTimeRemaining = FormatTimeSpan(stats.EstimatedTimeRemaining.Value);
            }

            if (!string.IsNullOrEmpty(e.CurrentOperation))
            {
                StatusMessage = e.CurrentOperation;
            }
        });
    }

    private void OnDownloadStatusChanged(object? sender, DownloadStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = e.Message ?? e.NewStatus.ToString();
            AddLogMessage($"Download status: {e.NewStatus} - {e.Message}");
            UpdateDownloadButtonStates();

            if (e.NewStatus == DownloadStatus.Completed)
            {
                var summary = _downloadManager.GetDownloadSummary();
                ShowDownloadSummary(summary);
            }
        });
    }

    private void OnDownloadErrorOccurred(object? sender, DownloadErrorEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var errorMessage = $"Error: {e.Exception.Message}";
            if (!string.IsNullOrEmpty(e.Context))
            {
                errorMessage = $"{e.Context}: {e.Exception.Message}";
            }
            AddLogMessage(errorMessage);
        });
    }

    private void UpdateDownloadButtonStates()
    {
        var status = _downloadManager.Status;
        CanStartDownload = IsAuthenticated && !string.IsNullOrEmpty(TargetDirectory) &&
                          (status == DownloadStatus.NotStarted || status == DownloadStatus.Completed ||
                           status == DownloadStatus.Cancelled || status == DownloadStatus.Error);
        CanPauseDownload = status == DownloadStatus.Downloading;
        CanResumeDownload = status == DownloadStatus.Paused;
        CanCancelDownload = status == DownloadStatus.Downloading || status == DownloadStatus.Paused;
    }

    private void OnAuthenticationStatusChanged(object? sender, AuthenticationStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsAuthenticated = e.IsAuthenticated;
            UserNickname = e.UserNickname;
            UpdateAuthenticationStatus();

            if (e.Error != null)
            {
                AddLogMessage($"Authentication error: {e.Error.Message}");
            }
        });
    }

    private void UpdateAuthenticationStatus()
    {
        AuthenticationStatus = IsAuthenticated 
            ? $"Authenticated as {UserNickname ?? "Unknown"}"
            : "Not authenticated";
    }

    private void AddLogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        LogMessages.Add($"[{timestamp}] {message}");
        
        // Keep only the last 100 messages
        while (LogMessages.Count > 100)
        {
            LogMessages.RemoveAt(0);
        }
    }

    public async Task InitializeAsync()
    {
        try
        {
            AddLogMessage("Initializing application...");

            // Update download button states initially
            UpdateDownloadButtonStates();

            // Try to load stored credentials
            var hasStoredCredentials = await _authService.LoadStoredCredentialsAsync();
            if (hasStoredCredentials)
            {
                AddLogMessage("Stored credentials loaded successfully");
            }
            else
            {
                AddLogMessage("No stored credentials found");
            }

            AddLogMessage("Application initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize application");
            AddLogMessage($"Initialization error: {ex.Message}");
            StatusMessage = $"Initialization failed: {ex.Message}";

            // Show error to user
            MessageBox.Show($"Application initialization failed: {ex.Message}\n\nPlease check your configuration and try again.",
                "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowDownloadSummary(DownloadSummary summary)
    {
        var message = $"Download completed!\n\n" +
                     $"Status: {summary.FinalStatus}\n" +
                     $"Total Photos: {summary.Statistics.TotalPhotos}\n" +
                     $"Downloaded: {summary.Statistics.DownloadedPhotos}\n" +
                     $"Failed: {summary.Statistics.FailedPhotos}\n" +
                     $"Total Time: {FormatTimeSpan(summary.TotalDuration)}\n" +
                     $"Average Speed: {(summary.Statistics.AverageDownloadSpeed.HasValue ? FormatBytes(summary.Statistics.AverageDownloadSpeed.Value) + "/s" : "N/A")}";

        if (summary.Errors.Any())
        {
            message += $"\n\nErrors: {summary.Errors.Count}";
        }

        MessageBox.Show(message, "Download Complete", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private static string FormatBytes(double bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = (decimal)bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }

    private static string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalDays >= 1)
            return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
        if (timeSpan.TotalHours >= 1)
            return $"{timeSpan.Hours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
        if (timeSpan.TotalMinutes >= 1)
            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        return $"{timeSpan.Seconds}s";
    }
}
