using System.Text.Json;
using Microsoft.Extensions.Logging;
using Winmug.Core.Authentication;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Improved SmugMug API client following best practices from API documentation
/// </summary>
public class ImprovedSmugMugApiClient : ISmugMugApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ILogger<ImprovedSmugMugApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";

    public ImprovedSmugMugApiClient(
        HttpClient httpClient,
        ISmugMugAuthenticationService authService,
        ILogger<ImprovedSmugMugApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    /// <summary>
    /// Gets the authenticated user information
    /// </summary>
    public async Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting authenticated user information...");

        // Use the correct endpoint without _expand=Node since we get Node URI directly in response
        // The _expand=Node parameter was causing issues and is not needed
        var url = $"{BaseApiUrl}!authuser";
        _logger.LogDebug("Making authenticated request to: {Url}", url);

        // First, let's get the raw response to see exactly what we're receiving
        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, url);
        request.Headers.Add("Accept", "application/json");

        _logger.LogInformation("Making authenticated request to: {Url}", url);
        _logger.LogDebug("Request headers: {Headers}", string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

        var httpResponse = await _httpClient.SendAsync(request, cancellationToken);
        var rawContent = await httpResponse.Content.ReadAsStringAsync(cancellationToken);

        _logger.LogInformation("Raw API Response Status: {StatusCode} {ReasonPhrase}", httpResponse.StatusCode, httpResponse.ReasonPhrase);
        _logger.LogInformation("Raw API Response Content Length: {Length}", rawContent.Length);
        _logger.LogDebug("Raw API Response Content: {Content}", rawContent.Length > 1000 ? rawContent.Substring(0, 1000) + "..." : rawContent);

        if (!httpResponse.IsSuccessStatusCode)
        {
            _logger.LogError("API request failed with status {StatusCode}: {Content}", httpResponse.StatusCode, rawContent);
            throw new InvalidOperationException($"Failed to get authenticated user information: {httpResponse.StatusCode} - {rawContent}");
        }

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response?.User == null)
        {
            _logger.LogError("Failed to get authenticated user - response was null or User was null");
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        var user = response.Response.User;

        // Enhanced logging to capture comprehensive user information
        _logger.LogInformation("📋 COMPREHENSIVE USER INFORMATION:");
        _logger.LogInformation("  Name: '{Name}'", user.Name ?? "NULL");
        _logger.LogInformation("  NickName: '{NickName}'", user.NickName ?? "NULL");
        _logger.LogInformation("  FirstName: '{FirstName}'", user.FirstName ?? "NULL");
        _logger.LogInformation("  LastName: '{LastName}'", user.LastName ?? "NULL");
        _logger.LogInformation("  Email: '{Email}'", user.Email ?? "NULL");
        _logger.LogInformation("  Plan: '{Plan}'", user.Plan ?? "NULL");
        _logger.LogInformation("  AccountStatus: '{AccountStatus}'", user.AccountStatus ?? "NULL");
        _logger.LogInformation("  ImageCount: {ImageCount}", user.ImageCount ?? 0);
        _logger.LogInformation("  WebUri: '{WebUri}'", user.WebUri ?? "NULL");
        _logger.LogInformation("  Uri: '{Uri}'", user.Uri ?? "NULL");
        _logger.LogInformation("  Has Uris: {HasUris}", user.Uris != null);

        // Check for expanded Node data (this is the key part from SmugMugAPI_Samples.md)
        if (response.Response.Node != null)
        {
            _logger.LogInformation("  ✅ EXPANDED NODE DATA FOUND:");
            _logger.LogInformation("    Node ID: '{NodeId}'", response.Response.Node.NodeId);
            _logger.LogInformation("    Node Name: '{NodeName}'", response.Response.Node.Name);
            _logger.LogInformation("    Node Type: '{NodeType}'", response.Response.Node.Type);
            _logger.LogInformation("    Node Has Children: {HasChildren}", response.Response.Node.HasChildren);
        }
        else
        {
            _logger.LogWarning("  ❌ NO EXPANDED NODE DATA - This indicates limited access permissions!");
        }

        // Log all available URIs from the response
        if (user.Uris != null)
        {
            _logger.LogInformation("📋 AVAILABLE URIs:");
            if (user.Uris.Node?.Uri != null)
                _logger.LogInformation("  ✅ Node URI: '{NodeUri}' -> Node ID: {NodeId}", user.Uris.Node.Uri, user.NodeId);
            if (user.Uris.Folder?.Uri != null)
                _logger.LogInformation("  ✅ Folder URI: '{FolderUri}'", user.Uris.Folder.Uri);
            if (user.Uris.UserAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserAlbums URI: '{UserAlbumsUri}'", user.Uris.UserAlbums.Uri);
            if (user.Uris.UserRecentImages?.Uri != null)
                _logger.LogInformation("  ✅ UserRecentImages URI: '{UserRecentImagesUri}'", user.Uris.UserRecentImages.Uri);
            if (user.Uris.UserFeaturedAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserFeaturedAlbums URI: '{UserFeaturedAlbumsUri}'", user.Uris.UserFeaturedAlbums.Uri);
        }
        else
        {
            _logger.LogWarning("  ❌ NO URIs FOUND - This indicates limited access permissions!");
        }

        _logger.LogInformation("Successfully retrieved user: {UserName} ({NickName})",
            user.EffectiveName, user.EffectiveNickName);

        return user;
    }

    /// <summary>
    /// Gets the user's root node using the Node ID from authuser response
    /// </summary>
    public async Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user root node using Node ID from authuser response...");

        // Step 1: Get authenticated user to retrieve Node ID
        var user = await GetAuthenticatedUserAsync(cancellationToken);
        _logger.LogDebug("User data retrieved: {UserName} ({NickName}) with Node ID: {NodeId}",
            user.Name, user.NickName, user.NodeId);

        // Step 2: Use the Node ID directly from the authuser response
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("✅ Using Node ID from authuser response: {NodeId}", user.NodeId);
            try
            {
                // Get the node details using the Node ID
                var rootNode = await GetNodeAsync(user.NodeId, cancellationToken);

                _logger.LogInformation("✅ Successfully retrieved root node: {NodeName} (ID: {NodeId}, Type: {Type})",
                    rootNode.Name, rootNode.NodeId, rootNode.Type);

                return rootNode;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get node using Node ID {NodeId}, trying fallback methods", user.NodeId);
            }
        }

        // Step 3: Fallback to Node URI approach if Node ID didn't work
        if (user.Uris?.Node?.Uri != null)
        {
            _logger.LogDebug("Fallback: Using Node URI from user response: {NodeUri}", user.Uris.Node.Uri);
            try
            {
                var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, user.Uris.Node.Uri, cancellationToken);

                if (response?.Response != null)
                {
                    _logger.LogInformation("✅ Successfully retrieved root node via Node URI: {NodeName} (ID: {NodeId}, Type: {Type})",
                        response.Response.Name, response.Response.NodeId, response.Response.Type);

                    return response.Response;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Node URI approach failed, trying fallback methods");
            }
        }
        else
        {
            _logger.LogWarning("No Node URI found in user response - this indicates limited OAuth permissions");
        }

        // Step 4: Try fallback approaches for limited access scenarios
        return await TryFallbackRootNodeApproaches(user, cancellationToken);
    }

    /// <summary>
    /// Try various fallback approaches to get the root node when primary method fails
    /// </summary>
    private async Task<SmugMugNode> TryFallbackRootNodeApproaches(SmugMugUser user, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Trying fallback approaches for root node access...");

        // Approach 1: Try !siteuser endpoint (alternative authenticated user endpoint)
        try
        {
            _logger.LogDebug("Fallback 1: Trying !siteuser endpoint");
            var siteUserUrl = $"{BaseApiUrl}!siteuser";

            // Try the nested structure first (like !authuser)
            try
            {
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.User?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.User.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (nested): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
            catch
            {
                // Try direct structure if nested fails
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (direct): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1 (!siteuser) failed");
        }

        // Approach 1b: Try direct authuser!node endpoint
        try
        {
            _logger.LogDebug("Fallback 1b: Trying !authuser!node");
            var nodeUrl = $"{BaseApiUrl}!authuser!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via authuser!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1b failed");
        }

        // Approach 2: Try user-specific node endpoint
        try
        {
            _logger.LogDebug("Fallback 2: Trying /user/{nickname}!node", user.NickName);
            var nodeUrl = $"{BaseApiUrl}/user/{user.NickName}!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via user!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 2 failed");
        }

        // Approach 3: Try to get user with Full response level
        try
        {
            _logger.LogDebug("Fallback 3: Trying user with Full response level");
            var fullUserUrl = $"{BaseApiUrl}/user/{user.NickName}?_responseLevel=Full";
            var fullUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, fullUserUrl, cancellationToken);

            if (fullUserResponse?.Response?.Uris?.Node?.Uri != null)
            {
                var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, fullUserResponse.Response.Uris.Node.Uri, cancellationToken);
                if (nodeResponse?.Response != null)
                {
                    _logger.LogInformation("✓ Root node found via Full response level: {NodeName} (ID: {NodeId})",
                        nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                    return nodeResponse.Response;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 3 failed");
        }

        // Approach 4: Create virtual root from albums (last resort)
        try
        {
            _logger.LogDebug("Fallback 4: Creating virtual root from accessible albums");
            var albumsUrl = $"{BaseApiUrl}/user/{user.NickName}!albums";
            var albumsResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<SmugMugAlbum>>>(HttpMethod.Get, albumsUrl, cancellationToken);

            if (albumsResponse?.Response != null && albumsResponse.Response.Any())
            {
                _logger.LogInformation("✓ Creating virtual root node - found {AlbumCount} accessible albums", albumsResponse.Response.Count);

                // Create a virtual root node since we can access albums but not the folder structure
                var virtualRoot = new SmugMugNode
                {
                    NodeId = "virtual-root",
                    Name = $"{user.Name}'s Photos",
                    Type = "Folder",
                    Description = "Virtual root - albums accessible with current permissions",
                    DateAdded = DateTime.Now,
                    DateModified = DateTime.Now,
                    HasChildren = true
                };

                return virtualRoot;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 4 failed");
        }

        // All approaches failed - provide detailed error information
        var errorMessage = "❌ UNABLE TO ACCESS FOLDER STRUCTURE\n\n" +
                          "This indicates insufficient OAuth permissions. The application needs 'Full' access to retrieve your folder structure.\n\n" +
                          "To fix this:\n" +
                          "1. Click 'Logout' to clear current credentials\n" +
                          "2. Click 'Authenticate with SmugMug' to start fresh\n" +
                          "3. When the SmugMug authorization page opens, make sure to:\n" +
                          "   - Click 'Allow' or 'Authorize' when prompted\n" +
                          "   - Grant full access permissions\n" +
                          "   - Do not select 'Public access only'\n\n" +
                          "If you continue to have issues, your SmugMug account may have restrictions that prevent folder structure access.";

        _logger.LogError(errorMessage);
        throw new InvalidOperationException(errorMessage);
    }

    /// <summary>
    /// Gets a specific node by its ID
    /// </summary>
    public async Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting node: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get node: {nodeId}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets the child nodes of a specific node using the correct API approach
    /// Uses the Node ID directly from the authuser response without _expand parameter
    /// </summary>
    public async Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting child nodes for: {NodeId} using direct Node API approach", nodeId);

        // Use the direct children endpoint without _expand parameter
        // The _expand=ChildNodes was causing issues, use direct approach instead
        var url = $"{BaseApiUrl}/node/{nodeId}!children";
        var nodes = new List<SmugMugNode>();

        await foreach (var node in GetPagedResultsAsync<SmugMugNode>(url, cancellationToken))
        {
            nodes.Add(node);
        }

        _logger.LogDebug("Found {Count} child nodes for: {NodeId}", nodes.Count, nodeId);
        return nodes;
    }

    /// <summary>
    /// Gets all child nodes recursively for a given node
    /// </summary>
    public async Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all child nodes recursively for: {NodeId}", nodeId);
        
        var allNodes = new List<SmugMugNode>();
        var nodesToProcess = new Queue<string>();
        nodesToProcess.Enqueue(nodeId);

        while (nodesToProcess.Count > 0)
        {
            var currentNodeId = nodesToProcess.Dequeue();
            var childNodes = await GetChildNodesAsync(currentNodeId, cancellationToken);
            
            foreach (var childNode in childNodes)
            {
                allNodes.Add(childNode);
                
                // If it's a folder, add it to the queue for recursive processing
                if (childNode.IsFolder)
                {
                    nodesToProcess.Enqueue(childNode.NodeId);
                }
            }
        }

        _logger.LogDebug("Found {Count} total child nodes recursively for: {NodeId}", allNodes.Count, nodeId);
        return allNodes;
    }

    /// <summary>
    /// Get the complete folder structure with album counts and size estimates using the legacy Folder API
    /// This provides more detailed folder information than the Node API
    /// </summary>
    public async Task<FolderNode> GetFolderStructureAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching complete folder structure using legacy Folder API for detailed information...");

        try
        {
            // Step 1: Get authenticated user to retrieve nickname
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            _logger.LogInformation("Using nickname '{NickName}' for folder API calls", user.NickName);

            // Step 2: Use the legacy Folder API for more detailed folder information
            // This endpoint provides richer folder details: /api/v2/folder/user/{nickname}
            var folderStructure = await GetFolderStructureUsingLegacyApiAsync(user.NickName, cancellationToken);

            _logger.LogInformation("Folder structure fetched successfully using legacy API. Total images: {ImageCount}, Total size: {Size}",
                folderStructure.TotalImageCount, folderStructure.TotalEstimatedSize);

            return folderStructure;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to fetch folder structure using legacy API, falling back to Node API");

            // Fallback to Node API if legacy API fails
            try
            {
                _logger.LogInformation("Attempting fallback to Node API approach...");
                var rootNode = await GetUserRootNodeAsync(cancellationToken);

                // Check if this is a virtual root (limited access scenario)
                if (rootNode.NodeId == "virtual-root")
                {
                    _logger.LogInformation("Building folder structure for virtual root (limited access mode)");
                    return await BuildVirtualRootFolderStructureAsync(cancellationToken);
                }

                // Normal folder structure building using Node API
                var folderStructure = await BuildFolderStructureRecursiveAsync(rootNode.NodeId, "", cancellationToken);

                _logger.LogInformation("Fallback folder structure fetched successfully. Total images: {ImageCount}, Total size: {Size}",
                    folderStructure.TotalImageCount, folderStructure.TotalEstimatedSize);

                return folderStructure;
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError(fallbackEx, "Both legacy Folder API and Node API approaches failed");
                throw new InvalidOperationException("Unable to retrieve folder structure using either legacy Folder API or Node API", fallbackEx);
            }
        }
    }

    /// <summary>
    /// Get folder structure using the legacy Folder API which provides more detailed information
    /// Uses endpoint: /api/v2/folder/user/{nickname}
    /// </summary>
    private async Task<FolderNode> GetFolderStructureUsingLegacyApiAsync(string nickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🗂️ Using legacy Folder API for detailed folder structure: /api/v2/folder/user/{Nickname}", nickname);

        // Use the legacy Folder API endpoint for more detailed folder information
        var folderUrl = $"{BaseApiUrl}/folder/user/{nickname}";
        _logger.LogDebug("Making request to legacy Folder API: {Url}", folderUrl);

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugFolder>>(HttpMethod.Get, folderUrl, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get folder structure from legacy API for user: {nickname}");
        }

        var rootFolder = response.Response;
        _logger.LogInformation("✅ Successfully retrieved root folder from legacy API: {FolderName}", rootFolder.Name);
        _logger.LogDebug("Root folder details - Type: {Type}, HasChildren: {HasChildren}, ChildCount: {ChildCount}",
            rootFolder.Type, rootFolder.HasChildren, rootFolder.ChildCount);

        // Convert SmugMugFolder to FolderNode with detailed information
        var folderNode = new FolderNode
        {
            NodeId = rootFolder.NodeId ?? "legacy-root",
            Name = rootFolder.Name ?? $"{nickname}'s Photos",
            Description = rootFolder.Description ?? "Root folder from legacy Folder API",
            Type = "Folder",
            UrlName = rootFolder.UrlName ?? "",
            FullPath = "",
            DateCreated = rootFolder.DateAdded ?? DateTime.MinValue,
            DateModified = rootFolder.DateModified ?? DateTime.MinValue,
            HasChildren = rootFolder.HasChildren ?? false
        };

        // If the folder has children, recursively build the structure
        if (rootFolder.HasChildren == true && !string.IsNullOrEmpty(rootFolder.NodeId))
        {
            _logger.LogInformation("📁 Root folder has children, building recursive structure...");
            await BuildLegacyFolderStructureRecursiveAsync(folderNode, rootFolder.NodeId, "", cancellationToken);
        }

        _logger.LogInformation("🎯 Legacy folder structure complete - Total: {AlbumCount} albums, {ImageCount} images, {Size} estimated size",
            folderNode.Albums.Count, folderNode.TotalImageCount, FormatBytes(folderNode.TotalEstimatedSizeBytes));

        return folderNode;
    }

    /// <summary>
    /// Recursively build folder structure using legacy Folder API and Node API combination
    /// </summary>
    private async Task BuildLegacyFolderStructureRecursiveAsync(FolderNode parentFolder, string nodeId, string parentPath, CancellationToken cancellationToken)
    {
        _logger.LogDebug("🔍 Building structure for node: {NodeId} (path: {Path})", nodeId, parentPath);

        try
        {
            // Get child nodes using the Node API (this works well for navigation)
            var childNodes = await GetChildNodesAsync(nodeId, cancellationToken);
            _logger.LogDebug("Found {ChildCount} child nodes for {NodeId}", childNodes.Count, nodeId);

            foreach (var childNode in childNodes)
            {
                var childPath = string.IsNullOrEmpty(parentPath) ? childNode.Name : $"{parentPath}/{childNode.Name}";

                if (childNode.IsAlbum)
                {
                    // This is an album - get detailed album information
                    _logger.LogDebug("📸 Processing album: {AlbumName} (Node: {NodeId})", childNode.Name, childNode.NodeId);
                    var albumInfo = await GetAlbumInfoAsync(childNode, childPath, cancellationToken);
                    parentFolder.Albums.Add(albumInfo);
                    parentFolder.TotalImageCount += albumInfo.ImageCount;
                    parentFolder.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
                }
                else if (childNode.IsFolder)
                {
                    // This is a subfolder - create folder node and recurse
                    _logger.LogDebug("📁 Processing subfolder: {FolderName} (Node: {NodeId})", childNode.Name, childNode.NodeId);

                    var subFolder = new FolderNode
                    {
                        NodeId = childNode.NodeId,
                        Name = childNode.Name,
                        Description = childNode.Description ?? "",
                        Type = childNode.Type,
                        UrlName = childNode.UrlName ?? "",
                        FullPath = childPath,
                        DateCreated = childNode.DateAdded ?? DateTime.MinValue,
                        DateModified = childNode.DateModified ?? DateTime.MinValue,
                        HasChildren = childNode.HasChildren ?? false
                    };

                    // Recursively build the subfolder structure
                    if (childNode.HasChildren == true)
                    {
                        await BuildLegacyFolderStructureRecursiveAsync(subFolder, childNode.NodeId, childPath, cancellationToken);
                    }

                    parentFolder.Children.Add(subFolder);
                    parentFolder.TotalImageCount += subFolder.TotalImageCount;
                    parentFolder.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
                }
                else
                {
                    _logger.LogDebug("❓ Unknown node type: {Type} for {NodeName}", childNode.Type, childNode.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build structure for node {NodeId}", nodeId);
            // Continue processing other nodes even if one fails
        }
    }

    /// <summary>
    /// Build folder structure for virtual root (limited access scenario)
    /// </summary>
    private async Task<FolderNode> BuildVirtualRootFolderStructureAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Building virtual folder structure from accessible albums...");

        var user = await GetAuthenticatedUserAsync(cancellationToken);

        var virtualRoot = new FolderNode
        {
            NodeId = "virtual-root",
            Name = $"{user.Name}'s Photos",
            Description = "Virtual root - showing accessible albums with current permissions",
            Type = "Folder",
            FullPath = "",
            DateCreated = DateTime.Now,
            DateModified = DateTime.Now,
            HasChildren = true
        };

        try
        {
            // Get all accessible albums
            var albumsUrl = $"{BaseApiUrl}/user/{user.NickName}!albums";
            var albums = new List<SmugMugAlbum>();

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(albumsUrl, cancellationToken))
            {
                albums.Add(album);
            }

            _logger.LogInformation("Found {AlbumCount} accessible albums for virtual root", albums.Count);

            // Convert albums to AlbumInfo objects
            foreach (var album in albums)
            {
                var albumInfo = new AlbumInfo
                {
                    AlbumKey = album.AlbumKey,
                    NodeId = "", // No node ID available in limited access
                    Name = album.Name,
                    Description = album.Description ?? "",
                    UrlName = album.UrlName ?? "",
                    FullPath = $"/{album.Name}",
                    ImageCount = album.ImageCount ?? 0,
                    EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                    DateCreated = album.Date ?? DateTime.MinValue,
                    DateModified = album.LastUpdated ?? DateTime.MinValue,
                    AllowDownloads = album.AllowDownloads ?? true
                };

                virtualRoot.Albums.Add(albumInfo);
                virtualRoot.TotalImageCount += albumInfo.ImageCount;
                virtualRoot.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }

            _logger.LogInformation("Virtual folder structure created with {AlbumCount} albums, {ImageCount} total images",
                virtualRoot.Albums.Count, virtualRoot.TotalImageCount);

            return virtualRoot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to build virtual folder structure");
            throw new InvalidOperationException("Unable to access albums even with limited permissions. Please re-authenticate with full access.", ex);
        }
    }

    /// <summary>
    /// Recursively build the folder structure using the Node API
    /// </summary>
    private async Task<FolderNode> BuildFolderStructureRecursiveAsync(string nodeId, string parentPath, CancellationToken cancellationToken = default)
    {
        var nodeInfo = await GetNodeAsync(nodeId, cancellationToken);
        var fullPath = string.IsNullOrEmpty(parentPath) ? nodeInfo.Name : $"{parentPath}/{nodeInfo.Name}";

        var folderNode = new FolderNode
        {
            NodeId = nodeId,
            Name = nodeInfo.Name,
            Description = nodeInfo.Description ?? "",
            Type = nodeInfo.Type,
            UrlName = nodeInfo.UrlName ?? "",
            FullPath = fullPath,
            DateCreated = nodeInfo.DateAdded ?? DateTime.MinValue,
            DateModified = nodeInfo.DateModified ?? DateTime.MinValue,
            HasChildren = nodeInfo.HasChildren ?? false
        };

        // Get child nodes
        var childNodes = await GetChildNodesAsync(nodeId, cancellationToken);

        foreach (var childNode in childNodes)
        {
            if (childNode.IsAlbum)
            {
                // This is an album - get album details
                var albumInfo = await GetAlbumInfoAsync(childNode, fullPath, cancellationToken);
                folderNode.Albums.Add(albumInfo);
                folderNode.TotalImageCount += albumInfo.ImageCount;
                folderNode.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }
            else if (childNode.IsFolder)
            {
                // This is a subfolder - recurse
                var subFolder = await BuildFolderStructureRecursiveAsync(childNode.NodeId, fullPath, cancellationToken);
                folderNode.Children.Add(subFolder);
                folderNode.TotalImageCount += subFolder.TotalImageCount;
                folderNode.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
            }
        }

        return folderNode;
    }

    /// <summary>
    /// Get album information from a node
    /// </summary>
    private async Task<AlbumInfo> GetAlbumInfoAsync(SmugMugNode albumNode, string parentPath, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get album details using the Album URI from the node
            if (albumNode.Uris?.Album?.Uri != null)
            {
                var albumResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, albumNode.Uris.Album.Uri, cancellationToken);
                if (albumResponse?.Response != null)
                {
                    var album = albumResponse.Response;
                    return new AlbumInfo
                    {
                        AlbumKey = album.AlbumKey,
                        NodeId = albumNode.NodeId,
                        Name = album.Name,
                        Description = album.Description ?? "",
                        UrlName = album.UrlName ?? "",
                        FullPath = $"{parentPath}/{album.Name}",
                        ImageCount = album.ImageCount ?? 0,
                        EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                        DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                        DateModified = album.LastUpdated ?? DateTime.MinValue,
                        AllowDownloads = album.AllowDownloads ?? true
                    };
                }
            }

            // Fallback: create album info from node data
            return new AlbumInfo
            {
                AlbumKey = "", // Will need to be populated later
                NodeId = albumNode.NodeId,
                Name = albumNode.Name,
                Description = albumNode.Description ?? "",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name}",
                ImageCount = 0, // Will need to be fetched separately
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = true // Default assumption
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get album details for node {NodeId}, using basic info", albumNode.NodeId);
            
            // Return basic album info if detailed fetch fails
            return new AlbumInfo
            {
                AlbumKey = "",
                NodeId = albumNode.NodeId,
                Name = albumNode.Name,
                Description = albumNode.Description ?? "",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name}",
                ImageCount = 0,
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = true
            };
        }
    }

    /// <summary>
    /// Estimate album size based on image count (rough approximation)
    /// </summary>
    private static long EstimateAlbumSize(int imageCount)
    {
        // Rough estimate: 5MB per image on average
        return imageCount * 5L * 1024 * 1024;
    }

    /// <summary>
    /// Verifies that we have the correct access level for private data
    /// </summary>
    public async Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            return !string.IsNullOrEmpty(user.NickName);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets detailed information about the user's access level and permissions
    /// </summary>
    public async Task<AccessLevelInfo> GetAccessLevelInfoAsync(CancellationToken cancellationToken = default)
    {
        var accessInfo = new AccessLevelInfo();
        _logger.LogInformation("🔍 DETAILED ACCESS LEVEL ANALYSIS STARTING...");

        try
        {
            _logger.LogInformation("Step 1: Getting authenticated user information...");
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            accessInfo.HasUserAccess = true;
            accessInfo.UserNickname = user.NickName;
            _logger.LogInformation("✅ User access confirmed: {UserName} ({NickName})", user.Name, user.NickName);

            // Check if user has Node URI (critical for folder access)
            if (user.Uris?.Node?.Uri != null)
            {
                _logger.LogInformation("✅ Node URI found: {NodeUri}", user.Uris.Node.Uri);
                accessInfo.HasPrivateAccess = true;
            }
            else
            {
                _logger.LogWarning("❌ NO NODE URI FOUND - This indicates LIMITED ACCESS");
                _logger.LogWarning("   This means you cannot access your folder structure");
                _logger.LogWarning("   Possible causes:");
                _logger.LogWarning("   - OAuth permissions were not granted properly");
                _logger.LogWarning("   - Different authentication session than browser");
                _logger.LogWarning("   - SmugMug account restrictions");
                accessInfo.HasPrivateAccess = false;
            }

            try
            {
                _logger.LogInformation("Step 2: Attempting to access root node...");
                var rootNode = await GetUserRootNodeAsync(cancellationToken);
                accessInfo.HasNodeAccess = true;
                accessInfo.RootNodeId = rootNode.NodeId;
                _logger.LogInformation("✅ Root node access confirmed: {NodeId}", rootNode.NodeId);
            }
            catch (Exception nodeEx)
            {
                _logger.LogWarning("❌ Root node access failed: {Error}", nodeEx.Message);
                accessInfo.HasNodeAccess = false;
            }

            // Determine overall access level
            if (accessInfo.HasPrivateAccess && accessInfo.HasNodeAccess)
            {
                accessInfo.AccessLevel = "Full";
                accessInfo.CanAccessPrivateContent = true;
                _logger.LogInformation("🎉 FULL ACCESS CONFIRMED - Can access private photos and folder structure");
            }
            else if (accessInfo.HasUserAccess)
            {
                accessInfo.AccessLevel = "Limited";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogWarning("⚠️ LIMITED ACCESS DETECTED - Can only access public content");

                var recommendations = new List<string>();
                if (!accessInfo.HasPrivateAccess)
                {
                    recommendations.Add("Re-authenticate and ensure you grant full permissions");
                    recommendations.Add("Make sure to click 'Authorize' or 'Allow' when prompted");
                }
                if (!accessInfo.HasNodeAccess)
                {
                    recommendations.Add("Cannot access folder structure - may need different OAuth scope");
                }
                accessInfo.Recommendations = recommendations.ToArray();
            }
            else
            {
                accessInfo.AccessLevel = "None";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogError("❌ NO ACCESS - Authentication failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Access level analysis failed");
            accessInfo.HasUserAccess = false;
            accessInfo.AccessLevel = "Unknown";
            accessInfo.CanAccessPrivateContent = false;
            accessInfo.Recommendations = new[] { "Authentication failed. Please try re-authenticating with SmugMug." };
        }

        _logger.LogInformation("🔍 ACCESS LEVEL ANALYSIS COMPLETE:");
        _logger.LogInformation("   Access Level: {AccessLevel}", accessInfo.AccessLevel);
        _logger.LogInformation("   Can Access Private Content: {CanAccess}", accessInfo.CanAccessPrivateContent);
        _logger.LogInformation("   Has User Access: {HasUser}", accessInfo.HasUserAccess);
        _logger.LogInformation("   Has Private Access: {HasPrivate}", accessInfo.HasPrivateAccess);
        _logger.LogInformation("   Has Node Access: {HasNode}", accessInfo.HasNodeAccess);

        return accessInfo;
    }

    /// <summary>
    /// Gets album information for a specific album
    /// </summary>
    public async Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get album: {albumKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets all images in a specific album
    /// </summary>
    public async Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting images for album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        var images = new List<SmugMugImage>();

        await foreach (var image in GetPagedResultsAsync<SmugMugImage>(url, cancellationToken))
        {
            images.Add(image);
        }

        _logger.LogDebug("Found {Count} images in album: {AlbumKey}", images.Count, albumKey);
        return images;
    }

    /// <summary>
    /// Gets the size details for a specific image
    /// </summary>
    public async Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting size details for image: {ImageKey}", imageKey);

        var url = $"{BaseApiUrl}/image/{imageKey}!sizes";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugImageSizes>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get image sizes: {imageKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Downloads image data from the specified URL
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        return await DownloadImageAsync(imageUrl, null, cancellationToken);
    }

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Downloading image from: {ImageUrl}", imageUrl);

        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, imageUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

        response.EnsureSuccessStatusCode();

        var contentLength = response.Content.Headers.ContentLength;
        var stream = await response.Content.ReadAsStreamAsync(cancellationToken);

        if (progress != null && contentLength.HasValue)
        {
            return new ProgressStream(stream, contentLength.Value, progress);
        }

        return stream;
    }

    /// <summary>
    /// Send an authenticated request to the SmugMug API
    /// </summary>
    private async Task<T?> SendAuthenticatedRequestAsync<T>(HttpMethod method, string url, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("🔍 Creating authenticated request for {Method} {Url}", method.Method, url);

            var request = _authService.CreateAuthenticatedRequest(method, url);
            request.Headers.Add("Accept", "application/json");

            _logger.LogDebug("📤 Sending {Method} request to: {Url}", method.Method, url);
            _logger.LogDebug("📋 Request headers: {Headers}",
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogInformation("📥 Response status: {StatusCode} {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("❌ API request failed: {StatusCode} {ReasonPhrase} - {Content}",
                    response.StatusCode, response.ReasonPhrase, errorContent);

                // Special handling for OAuth signature errors
                if (errorContent.Contains("oauth_problem=signature_invalid"))
                {
                    _logger.LogError("🚨 OAUTH SIGNATURE INVALID - This indicates an issue with OAuth signature generation");
                    _logger.LogError("   URL: {Url}", url);
                    _logger.LogError("   Method: {Method}", method.Method);
                    _logger.LogError("   This might be due to:");
                    _logger.LogError("   1. Incorrect OAuth parameter encoding");
                    _logger.LogError("   2. Wrong signature base string construction");
                    _logger.LogError("   3. Mismatched consumer secret or access token secret");
                    _logger.LogError("   4. Timestamp/nonce issues");
                }

                throw new HttpRequestException($"API request failed: {response.StatusCode} {response.ReasonPhrase}");
            }

            var jsonContent = await response.Content.ReadAsStringAsync(cancellationToken);

            // Enhanced logging for debugging user data issues
            if (url.Contains("!authuser") || url.Contains("!siteuser"))
            {
                _logger.LogDebug("Raw API response from {Url}:", url);
                _logger.LogDebug("Response content: {Content}", jsonContent.Length > 1000 ? jsonContent.Substring(0, 1000) + "..." : jsonContent);
            }

            return JsonSerializer.Deserialize<T>(jsonContent, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "💥 Failed to send authenticated request to: {Url}", url);
            throw;
        }
    }

    /// <summary>
    /// Get paged results from a SmugMug API endpoint
    /// </summary>
    private async IAsyncEnumerable<T> GetPagedResultsAsync<T>(string baseUrl, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var start = 1;
        const int pageSize = 100;
        bool hasMore = true;

        while (hasMore && !cancellationToken.IsCancellationRequested)
        {
            var url = $"{baseUrl}?_start={start}&_count={pageSize}";
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<T>>>(HttpMethod.Get, url, cancellationToken);

            if (response?.Response == null || !response.Response.Any())
            {
                hasMore = false;
                yield break;
            }

            foreach (var item in response.Response)
            {
                yield return item;
            }

            // Check if we got a full page, indicating there might be more
            hasMore = response.Response.Count == pageSize;
            start += pageSize;
        }
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
