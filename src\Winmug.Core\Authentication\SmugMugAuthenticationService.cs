using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using Winmug.Core.Services;

namespace Winmug.Core.Authentication;

/// <summary>
/// Implementation of SmugMug OAuth 1.0a authentication service
/// </summary>
public class SmugMugAuthenticationService : ISmugMugAuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly ISecureCredentialStorage _credentialStorage;
    private readonly SmugMugOAuthOptions _options;
    private readonly ILogger<SmugMugAuthenticationService> _logger;
    private readonly OAuthCredentials _credentials;

    public OAuthCredentials Credentials => _credentials;
    public bool IsAuthenticated => _credentials.IsAuthenticated;

    public event EventHandler<AuthenticationStatusChangedEventArgs>? AuthenticationStatusChanged;

    public SmugMugAuthenticationService(
        HttpClient httpClient,
        ISecureCredentialStorage credentialStorage,
        IOptions<SmugMugOAuthOptions> options,
        ILogger<SmugMugAuthenticationService> logger)
    {
        _httpClient = httpClient;
        _credentialStorage = credentialStorage;
        _options = options.Value;
        _logger = logger;
        _credentials = new OAuthCredentials
        {
            ConsumerKey = _options.ConsumerKey,
            ConsumerSecret = _options.ConsumerSecret
        };
    }

    public async Task<RequestTokenResponse> InitiateAuthenticationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initiating OAuth authentication flow");

            var parameters = new Dictionary<string, string>
            {
                ["oauth_callback"] = _options.CallbackUrl,
                ["oauth_consumer_key"] = _credentials.ConsumerKey,
                ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
                ["oauth_version"] = "1.0"
            };

            // Debug OAuth request before signing
            OAuthDebugger.LogOAuthRequest(_logger, "POST", _options.RequestTokenUrl, parameters, _credentials.ConsumerSecret);

            var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
                "POST", _options.RequestTokenUrl, parameters, _credentials.ConsumerSecret);
            parameters["oauth_signature"] = signature;

            var authHeader = CreateAuthorizationHeader(parameters);
            var request = new HttpRequestMessage(HttpMethod.Post, _options.RequestTokenUrl);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Sending request token request to: {Url}", _options.RequestTokenUrl);
            _logger.LogDebug("Authorization Header: {AuthHeader}", authHeader);
            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Request token request failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Request token request failed with status {response.StatusCode}: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseParams = ParseQueryString(responseContent);

            var requestToken = responseParams["oauth_token"];
            var requestTokenSecret = responseParams["oauth_token_secret"];
            var callbackConfirmed = responseParams.GetValueOrDefault("oauth_callback_confirmed") == "true";

            // Construct the OAuth authorization URL using the EXACT working structure from browser
            // This uses the login wrapper that ensures FULL ACCESS permissions
            var innerOAuthUrl = $"https://api.smugmug.com/services/oauth/1.0a/authorize?Access={Uri.EscapeDataString(_options.Access)}&oauth_token={Uri.EscapeDataString(requestToken)}";
            var encodedInnerUrl = Uri.EscapeDataString(innerOAuthUrl);

            // Generate goToToken (this is a JWT-like token that SmugMug uses for security)
            var goToTokenPayload = $"{{\"string\":\"{innerOAuthUrl}\",\"time\":{DateTimeOffset.UtcNow.ToUnixTimeSeconds()},\"signature\":\"placeholder\",\"version\":1,\"algorithm\":\"sha1\"}}";
            var goToTokenBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(goToTokenPayload));

            var authUrl = $"https://secure.smugmug.com/login?goTo={encodedInnerUrl}&goToToken={goToTokenBase64}&noThirdPartyLogin=1&showSignUpButton=true&user=&oauthLogin=1&Ureferrer=0&code=0&oauth_token={Uri.EscapeDataString(requestToken)}&Access={Uri.EscapeDataString(_options.Access)}&Permissions={Uri.EscapeDataString(_options.Permissions)}";

            _logger.LogInformation("Request token obtained successfully");
            _logger.LogInformation("🔗 FULL ACCESS OAUTH URL (Browser-Compatible Structure):");
            _logger.LogInformation("  Inner OAuth URL: {InnerUrl}", innerOAuthUrl);
            _logger.LogInformation("  Full Authorization URL: {AuthUrl}", authUrl);
            _logger.LogInformation("  OAuth Access level: {Access}, Permissions: {Permissions}", _options.Access, _options.Permissions);
            _logger.LogInformation("  🎯 Using EXACT browser structure that grants FULL ACCESS");
            _logger.LogInformation("  📱 This should show a 6-digit verification code AND grant full permissions");
            _logger.LogDebug("Request token: {RequestToken}", requestToken);
            _logger.LogDebug("Callback confirmed: {CallbackConfirmed}", callbackConfirmed);

            return new RequestTokenResponse
            {
                Token = requestToken,
                TokenSecret = requestTokenSecret,
                AuthorizationUrl = authUrl,
                CallbackConfirmed = callbackConfirmed
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate OAuth authentication");
            throw;
        }
    }

    public async Task<AccessTokenResponse> CompleteAuthenticationAsync(
        string verificationCode,
        string requestToken,
        string requestTokenSecret,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Completing OAuth authentication flow");

            var parameters = new Dictionary<string, string>
            {
                ["oauth_consumer_key"] = _credentials.ConsumerKey,
                ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
                ["oauth_token"] = requestToken,
                ["oauth_verifier"] = verificationCode,
                ["oauth_version"] = "1.0"
            };

            var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
                "POST", _options.AccessTokenUrl, parameters, _credentials.ConsumerSecret, requestTokenSecret);
            parameters["oauth_signature"] = signature;

            var authHeader = CreateAuthorizationHeader(parameters);
            var request = new HttpRequestMessage(HttpMethod.Post, _options.AccessTokenUrl);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Sending access token request to: {Url}", _options.AccessTokenUrl);
            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Access token request failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);

                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    throw new HttpRequestException("Authentication failed: Invalid verification code or expired session. Please try again.");
                }

                throw new HttpRequestException($"Access token request failed with status {response.StatusCode}: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseParams = ParseQueryString(responseContent);

            var accessToken = responseParams["oauth_token"];
            var accessTokenSecret = responseParams["oauth_token_secret"];
            var userNickname = responseParams.GetValueOrDefault("oauth_screen_name") ?? responseParams.GetValueOrDefault("oauth_user_nickname");

            _logger.LogDebug("Received access token: {Token}...", accessToken?.Substring(0, 8));
            _logger.LogDebug("Received access token secret: {Secret}...", accessTokenSecret?.Substring(0, 8));

            // Update credentials in memory
            _credentials.AccessToken = accessToken;
            _credentials.AccessTokenSecret = accessTokenSecret;

            _logger.LogDebug("Updated in-memory credentials. IsAuthenticated: {IsAuthenticated}", _credentials.IsAuthenticated);

            // Store credentials securely
            if (accessToken != null && accessTokenSecret != null)
            {
                await _credentialStorage.StoreCredentialsAsync(accessToken, accessTokenSecret, userNickname);
            }
            else
            {
                _logger.LogError("Cannot store credentials - accessToken or accessTokenSecret is null");
                throw new InvalidOperationException("OAuth response did not contain valid access token or secret");
            }

            _logger.LogInformation("OAuth authentication completed successfully for user: {UserNickname}", userNickname);
            _logger.LogDebug("Final credential check - AccessToken: {HasToken}, AccessTokenSecret: {HasSecret}",
                !string.IsNullOrEmpty(_credentials.AccessToken), !string.IsNullOrEmpty(_credentials.AccessTokenSecret));

            // Raise authentication status changed event
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, userNickname));

            return new AccessTokenResponse
            {
                Token = accessToken,
                TokenSecret = accessTokenSecret,
                UserNickname = userNickname
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete OAuth authentication");
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false, error: ex));
            throw;
        }
    }

    public async Task<bool> LoadStoredCredentialsAsync()
    {
        try
        {
            _logger.LogDebug("Attempting to load stored credentials...");
            var storedCredentials = await _credentialStorage.RetrieveCredentialsAsync();
            if (storedCredentials != null)
            {
                _logger.LogDebug("Found stored credentials for user: {UserNickname}", storedCredentials.UserNickname);
                _logger.LogDebug("Stored access token: {Token}...", storedCredentials.AccessToken?.Substring(0, 8));
                _logger.LogDebug("Stored access token secret: {Secret}...", storedCredentials.AccessTokenSecret?.Substring(0, 8));

                _credentials.AccessToken = storedCredentials.AccessToken;
                _credentials.AccessTokenSecret = storedCredentials.AccessTokenSecret;

                _logger.LogDebug("Credentials loaded into memory. IsAuthenticated: {IsAuthenticated}", _credentials.IsAuthenticated);
                _logger.LogInformation("Stored credentials loaded successfully for user: {UserNickname}", storedCredentials.UserNickname);
                AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, storedCredentials.UserNickname));
                return true;
            }

            _logger.LogDebug("No stored credentials found");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load stored credentials");
            return false;
        }
    }

    public async Task StoreCredentialsAsync()
    {
        if (!IsAuthenticated)
        {
            throw new InvalidOperationException("Cannot store credentials when not authenticated");
        }

        await _credentialStorage.StoreCredentialsAsync(_credentials.AccessToken!, _credentials.AccessTokenSecret!);
    }

    public async Task ClearStoredCredentialsAsync()
    {
        await _credentialStorage.ClearCredentialsAsync();
        _credentials.AccessToken = null;
        _credentials.AccessTokenSecret = null;

        _logger.LogInformation("Credentials cleared");
        AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false));
    }

    public HttpRequestMessage CreateAuthenticatedRequest(
        HttpMethod httpMethod,
        string url,
        Dictionary<string, string>? parameters = null)
    {
        _logger.LogDebug("CreateAuthenticatedRequest called for {Method} {Url}", httpMethod.Method, url);
        _logger.LogDebug("Current authentication status: {IsAuthenticated}", IsAuthenticated);
        _logger.LogDebug("AccessToken exists: {HasToken}", !string.IsNullOrEmpty(_credentials.AccessToken));
        _logger.LogDebug("AccessTokenSecret exists: {HasSecret}", !string.IsNullOrEmpty(_credentials.AccessTokenSecret));

        if (!IsAuthenticated)
        {
            _logger.LogError("Cannot create authenticated request - not authenticated");
            _logger.LogError("AccessToken: {Token}", _credentials.AccessToken ?? "NULL");
            _logger.LogError("AccessTokenSecret: {Secret}", _credentials.AccessTokenSecret ?? "NULL");
            throw new InvalidOperationException("Cannot create authenticated request when not authenticated");
        }

        _logger.LogDebug("Creating authenticated request for {Method} {Url}", httpMethod.Method, url);
        _logger.LogDebug("Using access token: {Token}...", _credentials.AccessToken?.Substring(0, 8));
        _logger.LogDebug("Using access token secret: {Secret}...", _credentials.AccessTokenSecret?.Substring(0, 8));

        parameters ??= new Dictionary<string, string>();

        var oauthParameters = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = _credentials.ConsumerKey,
            ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
            ["oauth_token"] = _credentials.AccessToken!,
            ["oauth_version"] = "1.0"
        };

        _logger.LogDebug("OAuth parameters: {Parameters}", string.Join(", ", oauthParameters.Select(kvp => $"{kvp.Key}={kvp.Value}")));

        // Combine OAuth parameters with request parameters for signature generation
        var allParameters = new Dictionary<string, string>(parameters);
        foreach (var kvp in oauthParameters)
        {
            allParameters[kvp.Key] = kvp.Value;
        }

        _logger.LogDebug("Generating OAuth signature for {Method} {Url}", httpMethod.Method, url);
        // Use the fixed OAuth signature generator for better compatibility
        var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
            httpMethod.Method, url, allParameters, _credentials.ConsumerSecret, _credentials.AccessTokenSecret);
        oauthParameters["oauth_signature"] = signature;

        _logger.LogDebug("Generated signature: {Signature}...", signature.Substring(0, Math.Min(8, signature.Length)));

        var authHeader = CreateAuthorizationHeader(oauthParameters);

        // Validate URL before creating the request
        if (string.IsNullOrWhiteSpace(url))
        {
            _logger.LogError("Cannot create authenticated request: URL is null or empty");
            throw new ArgumentException("URL cannot be null or empty", nameof(url));
        }

        if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
        {
            _logger.LogError("Cannot create authenticated request: URL is malformed: '{Url}'", url);
            throw new UriFormatException($"The URL '{url}' is not a valid absolute URI");
        }

        try
        {
            var request = new HttpRequestMessage(httpMethod, url);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Full Authorization header: {Header}", authHeader);
            _logger.LogDebug("OAuth parameters used: {Parameters}",
                string.Join(", ", oauthParameters.Select(kvp => $"{kvp.Key}={kvp.Value.Substring(0, Math.Min(10, kvp.Value.Length))}...")));
            _logger.LogDebug("Successfully created authenticated request");

            return request;
        }
        catch (UriFormatException ex)
        {
            _logger.LogError(ex, "Failed to create HttpRequestMessage with URL: '{Url}'", url);
            throw new UriFormatException($"Invalid URL format: '{url}'. {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Tests the current credentials by making a simple API call
    /// </summary>
    public async Task<bool> TestCredentialsAsync(CancellationToken cancellationToken = default)
    {
        if (!IsAuthenticated)
        {
            _logger.LogWarning("Cannot test credentials - not authenticated");
            return false;
        }

        try
        {
            _logger.LogDebug("Testing credentials with simple API call...");
            var request = CreateAuthenticatedRequest(HttpMethod.Get, "https://api.smugmug.com/api/v2!authuser");
            request.Headers.Add("Accept", "application/json");

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogDebug("Credential test response: {StatusCode} {ReasonPhrase}",
                response.StatusCode, response.ReasonPhrase);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("Credential test successful. Response length: {Length}", content.Length);
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("Credential test failed: {StatusCode} - {Content}",
                    response.StatusCode, errorContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during credential test");
            return false;
        }
    }

    private static string CreateAuthorizationHeader(Dictionary<string, string> parameters)
    {
        var headerParams = parameters
            .Where(kvp => kvp.Key.StartsWith("oauth_"))
            .OrderBy(kvp => kvp.Key)
            .Select(kvp => $"{OAuthSignatureGenerator.UrlEncode(kvp.Key)}=\"{OAuthSignatureGenerator.UrlEncode(kvp.Value)}\"");

        return $"OAuth {string.Join(", ", headerParams)}";
    }

    private static Dictionary<string, string> ParseQueryString(string queryString)
    {
        var result = new Dictionary<string, string>();
        var pairs = queryString.Split('&');

        foreach (var pair in pairs)
        {
            var keyValue = pair.Split('=', 2);
            if (keyValue.Length == 2)
            {
                result[Uri.UnescapeDataString(keyValue[0])] = Uri.UnescapeDataString(keyValue[1]);
            }
        }

        return result;
    }
}
