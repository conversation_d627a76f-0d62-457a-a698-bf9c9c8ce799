# SmugMug OAuth Authentication Troubleshooting

## Current Status

✅ **Fixed**: UI Exception (NullReferenceException) - Application now starts properly
✅ **Fixed**: OAuth URLs corrected to use `api.smugmug.com` (per official SmugMug documentation)
✅ **Fixed**: 403 error during authorization - was caused by incorrect OAuth endpoint URLs

## Diagnostic Steps Added

### 1. Enhanced Logging
- Added detailed OAuth request debugging
- Added step-by-step signature generation logging
- Added HTTP response logging with headers and content

### 2. OAuth Test Button
- Added "Test OAuth" button to the UI
- Provides detailed diagnostic information
- Tests OAuth signature generation independently

### 3. OAuth Debugger
- Created `OAuthDebugger` class for detailed request analysis
- Logs all OAuth parameters and signature generation steps
- Masks sensitive information in logs

## Possible Causes of "Unauthorized" Error

### 1. Invalid API Credentials
**Symptoms**: 401 Unauthorized on request token request
**Solution**: Verify your SmugMug API key and secret are correct
- Log into your SmugMug account
- Go to API settings and verify the credentials
- Make sure the API key is active and not expired

### 2. Incorrect OAuth Signature
**Symptoms**: 401 Unauthorized despite correct credentials
**Solution**: Use the OAuth test feature to debug signature generation
- Click "Test OAuth" button in the application
- Check the logs for signature generation details
- Compare with known working OAuth implementations

### 3. Clock Synchronization Issues
**Symptoms**: Intermittent 401 errors
**Solution**: Ensure system clock is synchronized
- OAuth timestamps must be within a few minutes of server time
- Check Windows time synchronization settings

### 4. Network/Firewall Issues
**Symptoms**: Connection timeouts or unexpected errors
**Solution**: Check network connectivity
- Verify HTTPS connections to secure.smugmug.com work
- Check corporate firewall settings
- Try from a different network if possible

## Testing Steps

### Step 1: Verify Application Starts
1. Run the application
2. Verify no UI exceptions occur
3. Check that all buttons are visible

### Step 2: Test OAuth Signature Generation
1. Click "Test OAuth" button
2. Review the detailed logs in the Status and Log section
3. Look for any obvious issues in the OAuth parameters

### Step 3: Manual Verification
1. Compare the generated OAuth signature with online OAuth signature generators
2. Use the same parameters (timestamp, nonce, etc.) for comparison
3. Verify the signature base string is constructed correctly

### Step 4: Check API Credentials
1. Log into your SmugMug account
2. Verify the API key and secret in the application match your account
3. Check if the API key has the necessary permissions

## Expected OAuth Flow

1. **Request Token**: POST to `https://secure.smugmug.com/services/oauth/1.0a/getRequestToken`
   - Should return `oauth_token` and `oauth_token_secret`
   - Should return `oauth_callback_confirmed=true`

2. **User Authorization**: Browser opens to `https://secure.smugmug.com/services/oauth/1.0a/authorize`
   - User logs in with SmugMug credentials
   - User authorizes the application
   - User receives 6-digit verification code

3. **Access Token**: POST to `https://secure.smugmug.com/services/oauth/1.0a/getAccessToken`
   - Uses request token + verification code
   - Returns access token for API calls

## Latest Fix (January 2025)

### OAuth URL Correction
**Problem**: 403 errors during SmugMug authorization were caused by using incorrect OAuth endpoint URLs.

**Root Cause**: The application was using `secure.smugmug.com` for OAuth endpoints, but SmugMug's official documentation specifies `api.smugmug.com`.

**Fix**: Updated all OAuth URLs to use the correct endpoints:
- Request Token: `https://api.smugmug.com/services/oauth/1.0a/getRequestToken`
- Authorize: `https://api.smugmug.com/services/oauth/1.0a/authorize`
- Access Token: `https://api.smugmug.com/services/oauth/1.0a/getAccessToken`

## Next Steps

1. **Run the updated application** with the corrected OAuth URLs
2. **Test the authentication flow** - it should now work without 403 errors
3. **Click "Test OAuth"** if you still encounter issues
4. **Verify API credentials** in your SmugMug account settings if needed

## Files Modified for Diagnostics

- `src/Winmug.Core/Authentication/OAuthDebugger.cs` - OAuth request debugging
- `src/Winmug.Core/Authentication/OAuthTester.cs` - OAuth testing utility
- `src/Winmug/ViewModels/MainWindowViewModel.cs` - Added test command
- `src/Winmug/Views/MainWindow.xaml` - Added test button
- `src/Winmug.Core/Authentication/SmugMugAuthenticationService.cs` - Enhanced logging

The diagnostic tools should help identify the exact cause of the authentication failure.
